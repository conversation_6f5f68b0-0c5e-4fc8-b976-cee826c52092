package com.bilibili.admin.controller;

import cn.hutool.core.bean.BeanUtil;
import com.bilibili.common.model.dto.admin.category.SaveCateGoryRequest;
import com.bilibili.common.model.entity.Category;
import com.bilibili.common.model.vo.LoadCategoryResponse;
import com.bilibili.common.service.CategoryService;
import com.bilibili.common.utils.RedisCacheManager;
import com.bilibili.common.utils.Response;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@RestController
@RequestMapping("/category")
public class CategoryController {

    @Resource
    private CategoryService categoryService;


    @RequestMapping("/loadCategory")
    public Response<List<LoadCategoryResponse>> loadCategory() {
        List<LoadCategoryResponse> categoryList = categoryService.loadCategory();
        return Response.success(categoryList);
    }

    @RequestMapping("/saveCategory")
    public Response<Void> saveOrUpdateCategory(@RequestBody @Validated SaveCateGoryRequest saveCateGoryRequest) {
        Category category = BeanUtil.copyProperties(saveCateGoryRequest, Category.class);
        categoryService.saveOrUpdateCategory(category);
        return Response.success();
    }

    @RequestMapping("/delCategory")
    public Response<Void> deleteCategory(Integer categoryId) {
        categoryService.deleteCategory(categoryId);
        return Response.success();
    }

    @RequestMapping("/changeSort")
    public Response<Void> changeSort(String categoryIds) {
        categoryService.changeSort(categoryIds);
        return Response.success();
    }


}
