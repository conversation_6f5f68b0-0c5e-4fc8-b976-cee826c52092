package com.bilibili.common.component;

import com.bilibili.common.constants.RedisConstant;
import com.bilibili.common.constants.TimeConstant;
import com.bilibili.common.model.vo.LoadCategoryResponse;
import com.bilibili.common.model.vo.UserVO;
import com.bilibili.common.utils.RedisUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * 项目中涉及到redis操作的二次封装
 */
@Component
public class RedisComponent {

    @Resource
    private RedisUtil redisUtil;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 保存管理员登录信息
     * @param account 管理员账号
     * @return token
     */
    public String saveTokenInfo4Admin(String account) {
        String token = UUID.randomUUID().toString();
        String redisKey = RedisConstant.REDIS_ADMIN_TOKEN_PREFIX + token;
        redisUtil.setex(redisKey, account, TimeConstant.ONE_DAY_MILLIS);
        return token;
    }

    public void saveTokenInfo4Web(String token, UserVO userVO) {
        redisUtil.setex(RedisConstant.REDIS_WEB_TOKEN_PREFIX + token, userVO, TimeConstant.ONE_DAY_MILLIS * 7L);
    }

    /**
     * 获取存储在redis中的验证码
     * @param checkCodeKey 前端保存的验证码key
     * @return 验证码
     */
    public String getCheckCode(String checkCodeKey) {
        String redisKey = RedisConstant.REDIS_CAPTCHA_PREFIX + checkCodeKey;
        return (String) redisUtil.get(redisKey);
    }

    /**
     * 清除redis中的验证码
     * @param checkCodeKey 前端保存的验证码key
     */
    public void deleteCheckCode(String checkCodeKey) {
        String redisKey = RedisConstant.REDIS_CAPTCHA_PREFIX + checkCodeKey;
        redisUtil.delete(redisKey);
    }

    public void deleteToken4Admin(String token) {
        String redisKey = RedisConstant.REDIS_ADMIN_TOKEN_PREFIX + token;
        redisUtil.delete(redisKey);
    }

    /**
     * 保存验证码
     * @param checkCodeKey 前端保存的验证码key
     * @param checkCode 验证码
     */
    public void saveCheckCode(String checkCodeKey, String checkCode) {
        String checkCodeRedisKey = RedisConstant.REDIS_CAPTCHA_PREFIX + checkCodeKey;
        redisUtil.setex(checkCodeRedisKey, checkCode, TimeConstant.TEN_MINUTES_MILLIS);
    }


    public String getAdminToken(String token) {
        return (String) redisUtil.get(RedisConstant.REDIS_ADMIN_TOKEN_PREFIX + token);
    }

    public void saveCategoryList(List<LoadCategoryResponse> categoryList) {
        try {
            String json = objectMapper.writeValueAsString(categoryList);
            redisUtil.set(RedisConstant.REDIS_CATEGORY_LIST, json);
        } catch (Exception e) {
            throw new RuntimeException("保存分类列表到Redis失败", e);
        }
    }

    public List<LoadCategoryResponse> getCategoryList() {
        try {
            String json = (String) redisUtil.get(RedisConstant.REDIS_CATEGORY_LIST);
            if (json == null) {
                return null;
            }
            return objectMapper.readValue(json, new TypeReference<List<LoadCategoryResponse>>() {});
        } catch (Exception e) {
            throw new RuntimeException("从Redis获取分类列表失败", e);
        }
    }
}
