package com.bilibili.common.utils;

import com.bilibili.common.component.RedisComponent;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Redis缓存管理工具类
 * 用于处理缓存相关的管理操作，特别是序列化格式变更后的缓存清理
 */
@Component
public class RedisCacheManager {
    
    private static final Logger logger = LoggerFactory.getLogger(RedisCacheManager.class);
    
    @Resource
    private RedisComponent redisComponent;
    
    /**
     * 清理所有可能存在序列化问题的缓存
     * 在系统启动或序列化配置变更后调用
     */
    public void clearProblematicCaches() {
        try {
            logger.info("开始清理可能存在序列化问题的Redis缓存...");
            
            // 清理分类列表缓存
            redisComponent.clearCategoryListCache();
            logger.info("已清理分类列表缓存");
            
            logger.info("Redis缓存清理完成");
        } catch (Exception e) {
            logger.error("清理Redis缓存时发生错误", e);
        }
    }
    
    /**
     * 重新构建分类缓存
     */
    public void rebuildCategoryCache() {
        try {
            logger.info("开始重新构建分类缓存...");
            redisComponent.clearCategoryListCache();
            // 注意：这里不直接调用loadCategory，避免循环依赖
            // 实际的重建会在下次调用loadCategory时自动进行
            logger.info("分类缓存已标记为需要重建");
        } catch (Exception e) {
            logger.error("重建分类缓存时发生错误", e);
        }
    }
}
